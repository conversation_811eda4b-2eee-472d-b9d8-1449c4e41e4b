Locale = {}

Locale.error = {
    not_enough_money = "You don't have enough money",
    invalid_amount = "Invalid amount",
    account_not_found = "Account not found",
    insufficient_funds = "Insufficient funds",
    transfer_failed = "Transfer failed",
    loan_denied = "Loan application denied",
    already_have_loan = "You already have an active loan",
    invalid_account_number = "Invalid account number",
    same_account = "Cannot transfer to the same account",
    bank_closed = "Bank is currently closed",
    atm_error = "ATM is currently out of service"
}

Locale.success = {
    deposit_success = "Successfully deposited $%s",
    withdraw_success = "Successfully withdrew $%s",
    transfer_success = "Successfully transferred $%s to account %s",
    loan_approved = "Loan of $%s approved",
    loan_paid = "Loan payment of $%s processed",
    account_created = "New %s account created",
    pin_changed = "PIN successfully changed"
}

Locale.info = {
    current_balance = "Current Balance: $%s",
    account_number = "Account Number: %s",
    available_balance = "Available Balance: $%s",
    pending_transactions = "Pending Transactions: %s",
    loan_balance = "Outstanding Loan: $%s",
    next_payment = "Next Payment Due: %s",
    interest_earned = "Interest Earned Today: $%s"
}

Locale.ui = {
    bank_title = "QB Banking System",
    account_overview = "Account Overview",
    quick_actions = "Quick Actions",
    deposit = "Deposit",
    withdraw = "Withdraw",
    transfer = "Transfer",
    loan = "Loan",
    history = "Transaction History",
    settings = "Settings",
    logout = "Logout",
    amount = "Amount",
    account_number = "Account Number",
    description = "Description",
    confirm = "Confirm",
    cancel = "Cancel",
    submit = "Submit",
    close = "Close",
    balance = "Balance",
    date = "Date",
    type = "Type",
    status = "Status",
    pending = "Pending",
    completed = "Completed",
    failed = "Failed",
    loading = "Loading...",
    no_transactions = "No transactions found",
    enter_amount = "Enter amount",
    enter_account = "Enter account number",
    enter_description = "Enter description (optional)",
    minimum_amount = "Minimum amount: $%s",
    maximum_amount = "Maximum amount: $%s",
    transaction_fee = "Transaction fee: $%s",
    confirm_transaction = "Confirm Transaction",
    transaction_details = "Transaction Details",
    recipient = "Recipient",
    fee = "Fee",
    total = "Total",
    available_loans = "Available Loans",
    loan_amount = "Loan Amount",
    loan_term = "Loan Term (days)",
    interest_rate = "Interest Rate",
    monthly_payment = "Daily Payment",
    apply_loan = "Apply for Loan",
    pay_loan = "Pay Loan",
    loan_details = "Loan Details",
    remaining_balance = "Remaining Balance",
    next_payment_date = "Next Payment Date",
    change_pin = "Change PIN",
    current_pin = "Current PIN",
    new_pin = "New PIN",
    confirm_pin = "Confirm PIN",
    account_settings = "Account Settings",
    notifications = "Notifications",
    privacy = "Privacy Settings",
    security = "Security"
}

Locale.notifications = {
    deposit_received = "Deposit of $%s received",
    withdrawal_processed = "Withdrawal of $%s processed",
    transfer_received = "Transfer of $%s received from account %s",
    transfer_sent = "Transfer of $%s sent to account %s",
    loan_payment_due = "Loan payment of $%s is due",
    interest_credited = "Interest of $%s credited to your account",
    low_balance = "Low balance warning: $%s remaining",
    account_locked = "Account temporarily locked due to suspicious activity"
}

Locale.target = {
    bank_teller = "Talk to Bank Teller",
    atm_machine = "Use ATM",
    open_banking = "Access Banking Services"
}
