local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local bankOpen = false
local currentATM = nil
local bankPeds = {}

-- Initialize
CreateThread(function()
    PlayerData = QBCore.Functions.GetPlayerData()
end)

RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
    CreateBankBlips()
    CreateBankPeds()
    SetupATMs()
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    PlayerData = {}
end)

-- Create Bank Blips
function CreateBankBlips()
    for k, v in pairs(Config.BankLocations) do
        if v.blip then
            local blip = AddBlipForCoord(v.coords.x, v.coords.y, v.coords.z)
            SetBlipSprite(blip, Config.Blips[1].sprite)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, Config.Blips[1].scale)
            SetBlipColour(blip, Config.Blips[1].color)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentSubstringPlayerName(v.name)
            EndTextCommandSetBlipName(blip)
        end
    end
end

-- Create Bank Peds
function CreateBankPeds()
    for k, v in pairs(Config.BankLocations) do
        if v.ped then
            local pedHash = GetHashKey(v.ped.model)
            RequestModel(pedHash)
            while not HasModelLoaded(pedHash) do
                Wait(1)
            end
            
            local ped = CreatePed(4, pedHash, v.ped.coords.x, v.ped.coords.y, v.ped.coords.z - 1.0, v.ped.coords.w, false, true)
            SetEntityHeading(ped, v.ped.coords.w)
            FreezeEntityPosition(ped, true)
            SetEntityInvincible(ped, true)
            SetBlockingOfNonTemporaryEvents(ped, true)
            
            if v.ped.scenario then
                TaskStartScenarioInPlace(ped, v.ped.scenario, 0, true)
            end
            
            -- Add ox_target interaction
            exports.ox_target:addLocalEntity(ped, {
                {
                    name = 'bank_teller_' .. k,
                    icon = 'fas fa-university',
                    label = Locale.target.bank_teller,
                    onSelect = function()
                        OpenBankingUI()
                    end,
                    distance = 2.0
                }
            })
            
            bankPeds[k] = ped
        end
    end
end

-- Setup ATMs
function SetupATMs()
    for _, model in pairs(Config.ATMModels) do
        exports.ox_target:addModel(model, {
            {
                name = 'atm_banking',
                icon = 'fas fa-credit-card',
                label = Locale.target.atm_machine,
                onSelect = function(data)
                    currentATM = data.entity
                    OpenBankingUI(true)
                end,
                distance = 2.0
            }
        })
    end
end

-- Open Banking UI
function OpenBankingUI(isATM)
    if bankOpen then return end
    
    bankOpen = true
    SetNuiFocus(true, true)
    
    QBCore.Functions.TriggerCallback('qb-banking:server:GetAccountData', function(accountData)
        SendNUIMessage({
            action = 'openBank',
            accountData = accountData,
            isATM = isATM or false,
            playerName = PlayerData.charinfo.firstname .. ' ' .. PlayerData.charinfo.lastname
        })
    end)
end

-- Close Banking UI
function CloseBankingUI()
    bankOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = 'closeBank'
    })
    currentATM = nil
end

-- NUI Callbacks
RegisterNUICallback('closeBank', function(data, cb)
    CloseBankingUI()
    cb('ok')
end)

RegisterNUICallback('deposit', function(data, cb)
    local amount = tonumber(data.amount)
    if not amount or amount <= 0 then
        QBCore.Functions.Notify(Locale.error.invalid_amount, 'error')
        cb('error')
        return
    end
    
    QBCore.Functions.TriggerCallback('qb-banking:server:Deposit', function(success, message)
        if success then
            QBCore.Functions.Notify(string.format(Locale.success.deposit_success, amount), 'success')
            RefreshAccountData()
        else
            QBCore.Functions.Notify(message, 'error')
        end
        cb(success and 'success' or 'error')
    end, amount)
end)

RegisterNUICallback('withdraw', function(data, cb)
    local amount = tonumber(data.amount)
    if not amount or amount <= 0 then
        QBCore.Functions.Notify(Locale.error.invalid_amount, 'error')
        cb('error')
        return
    end
    
    QBCore.Functions.TriggerCallback('qb-banking:server:Withdraw', function(success, message)
        if success then
            QBCore.Functions.Notify(string.format(Locale.success.withdraw_success, amount), 'success')
            RefreshAccountData()
        else
            QBCore.Functions.Notify(message, 'error')
        end
        cb(success and 'success' or 'error')
    end, amount)
end)

RegisterNUICallback('transfer', function(data, cb)
    local amount = tonumber(data.amount)
    local targetAccount = data.targetAccount
    local description = data.description or ''
    
    if not amount or amount <= 0 then
        QBCore.Functions.Notify(Locale.error.invalid_amount, 'error')
        cb('error')
        return
    end
    
    if not targetAccount or targetAccount == '' then
        QBCore.Functions.Notify(Locale.error.invalid_account_number, 'error')
        cb('error')
        return
    end
    
    QBCore.Functions.TriggerCallback('qb-banking:server:Transfer', function(success, message)
        if success then
            QBCore.Functions.Notify(string.format(Locale.success.transfer_success, amount, targetAccount), 'success')
            RefreshAccountData()
        else
            QBCore.Functions.Notify(message, 'error')
        end
        cb(success and 'success' or 'error')
    end, amount, targetAccount, description)
end)

RegisterNUICallback('getTransactionHistory', function(data, cb)
    QBCore.Functions.TriggerCallback('qb-banking:server:GetTransactionHistory', function(transactions)
        cb(transactions)
    end, data.page or 1, data.limit or 10)
end)

RegisterNUICallback('applyLoan', function(data, cb)
    local amount = tonumber(data.amount)
    local term = tonumber(data.term)
    
    if not amount or amount <= 0 then
        QBCore.Functions.Notify(Locale.error.invalid_amount, 'error')
        cb('error')
        return
    end
    
    QBCore.Functions.TriggerCallback('qb-banking:server:ApplyLoan', function(success, message)
        if success then
            QBCore.Functions.Notify(string.format(Locale.success.loan_approved, amount), 'success')
            RefreshAccountData()
        else
            QBCore.Functions.Notify(message, 'error')
        end
        cb(success and 'success' or 'error')
    end, amount, term)
end)

RegisterNUICallback('payLoan', function(data, cb)
    local amount = tonumber(data.amount)
    
    QBCore.Functions.TriggerCallback('qb-banking:server:PayLoan', function(success, message)
        if success then
            QBCore.Functions.Notify(string.format(Locale.success.loan_paid, amount), 'success')
            RefreshAccountData()
        else
            QBCore.Functions.Notify(message, 'error')
        end
        cb(success and 'success' or 'error')
    end, amount)
end)

-- Refresh Account Data
function RefreshAccountData()
    QBCore.Functions.TriggerCallback('qb-banking:server:GetAccountData', function(accountData)
        SendNUIMessage({
            action = 'updateAccountData',
            accountData = accountData
        })
    end)
end

-- Events
RegisterNetEvent('qb-banking:client:RefreshUI', function()
    if bankOpen then
        RefreshAccountData()
    end
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        for k, v in pairs(bankPeds) do
            if DoesEntityExist(v) then
                DeleteEntity(v)
            end
        end
        if bankOpen then
            CloseBankingUI()
        end
    end
end)
