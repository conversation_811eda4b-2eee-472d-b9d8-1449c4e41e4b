# QB-Banking System

A comprehensive banking system for QBCore Framework with modern dark-themed UI, ATM integration, and advanced features.

## Features

### 🏦 Core Banking
- **Account Management**: Automatic account creation for new players
- **Deposits & Withdrawals**: Cash transactions with real-time balance updates
- **Money Transfers**: Send money between accounts with transaction fees
- **Transaction History**: Complete transaction logging with pagination
- **Multiple Account Types**: Checking, Savings, and Business accounts

### 💳 ATM Integration
- **ox_target Integration**: Interactive ATM machines throughout the map
- **ATM Locations**: Support for multiple ATM models
- **Limited ATM Functions**: Deposit, withdraw, and balance checking

### 🏛️ Bank Locations
- **Multiple Bank Branches**: 6 pre-configured bank locations
- **Interactive NPCs**: Bank tellers with ox_target integration
- **Custom Blips**: Bank and ATM markers on the map

### 💰 Loan System
- **Loan Applications**: Apply for loans with customizable terms
- **Interest Calculation**: Automatic interest and payment calculations
- **Payment Tracking**: Daily payment schedules with auto-deduction
- **Loan Management**: View remaining balance and payment history

### 🎨 Modern UI
- **Dark Theme**: Sleek, modern dark interface
- **Responsive Design**: Mobile-friendly responsive layout
- **Smooth Animations**: CSS animations and transitions
- **Interactive Elements**: Hover effects and visual feedback

### 📊 Advanced Features
- **Daily Interest**: Automatic interest calculation for different account types
- **Transaction Fees**: Configurable fees for transfers
- **Security Features**: PIN protection and account validation
- **Notification System**: In-game notifications for transactions
- **Database Optimization**: Indexed tables and stored procedures

## Installation

### Prerequisites
- QBCore Framework
- oxmysql
- ox_target

### Step 1: Download and Extract
1. Download the `qb-banking` resource
2. Extract to your `resources` folder
3. Ensure the folder is named `qb-banking`

### Step 2: Database Setup
1. Import the `qb-banking.sql` file into your database
2. This will create all necessary tables, indexes, and stored procedures

### Step 3: Server Configuration
1. Add `ensure qb-banking` to your `server.cfg`
2. Make sure it loads after QBCore, oxmysql, and ox_target

### Step 4: Dependencies
Ensure these resources are installed and running:
```
ensure qb-core
ensure oxmysql
ensure ox_target
ensure qb-banking
```

## Configuration

### Config.lua Settings

#### Banking Settings
```lua
Config.BankingFee = 0.02 -- 2% fee for transfers
Config.MaxTransferAmount = 100000 -- Maximum transfer amount
Config.MinTransferAmount = 1 -- Minimum transfer amount
Config.InterestRate = 0.001 -- Daily interest rate (0.1%)
```

#### Bank Locations
The config includes 6 pre-configured bank locations:
- Fleeca Bank (Downtown)
- Fleeca Bank (Hawick)
- Fleeca Bank (Great Ocean Highway)
- Fleeca Bank (Paleto Bay)
- Fleeca Bank (Sandy Shores)
- Pacific Standard Bank (Pillbox Hill)

#### ATM Models
Supported ATM models:
- `prop_atm_01`
- `prop_atm_02`
- `prop_atm_03`
- `prop_fleeca_atm`

#### Account Types
- **Checking**: 0.05% daily interest, $0 minimum, $500K maximum
- **Savings**: 0.2% daily interest, $100 minimum, $1M maximum
- **Business**: 0.1% daily interest, $1K minimum, $5M maximum

#### Loan Settings
```lua
Config.LoanSettings = {
    maxAmount = 50000,
    minAmount = 1000,
    interestRate = 0.05, -- 5% interest
    maxTerm = 30, -- 30 days
    minTerm = 7 -- 7 days
}
```

## Usage

### For Players

#### Accessing Banking Services
1. **Bank Branches**: Visit any bank location and interact with the teller
2. **ATM Machines**: Use ox_target on any ATM machine

#### Banking Operations
- **Deposit**: Convert cash to bank balance
- **Withdraw**: Convert bank balance to cash
- **Transfer**: Send money to another player's account
- **Loan**: Apply for loans with flexible terms
- **History**: View complete transaction history

#### Account Information
- View current balance and account number
- Check cash on hand
- Monitor loan status and payments
- Track transaction history

### For Developers

#### Events
```lua
-- Client Events
TriggerEvent('qb-banking:client:RefreshUI') -- Refresh banking UI

-- Server Events (examples)
TriggerServerEvent('qb-banking:server:Deposit', amount)
TriggerServerEvent('qb-banking:server:Withdraw', amount)
TriggerServerEvent('qb-banking:server:Transfer', amount, targetAccount, description)
```

#### Callbacks
```lua
-- Get account data
QBCore.Functions.TriggerCallback('qb-banking:server:GetAccountData', function(accountData)
    -- Handle account data
end)

-- Process transactions
QBCore.Functions.TriggerCallback('qb-banking:server:Deposit', function(success, message)
    -- Handle result
end, amount)
```

## Database Structure

### Tables
- `bank_accounts`: Player account information
- `bank_transactions`: Transaction history
- `bank_loans`: Loan information
- `bank_cards`: ATM card data (future feature)
- `bank_notifications`: System notifications
- `bank_settings`: User preferences

### Views
- `v_account_summary`: Complete account overview with loans and notifications

### Stored Procedures
- `sp_transfer_funds`: Secure money transfer with transaction integrity

## Customization

### Adding New Bank Locations
```lua
{
    name = "Your Bank Name",
    coords = vector3(x, y, z),
    blip = true,
    ped = {
        model = "ped_model",
        coords = vector4(x, y, z, heading),
        scenario = "WORLD_HUMAN_CLIPBOARD"
    }
}
```

### Modifying Interest Rates
Edit the `Config.AccountTypes` table in `config.lua`:
```lua
Config.AccountTypes = {
    checking = {
        name = "Checking Account",
        interestRate = 0.001, -- 0.1% daily
        minBalance = 0,
        maxBalance = 500000
    }
}
```

### Custom Transaction Types
Add new transaction types to `Config.TransactionTypes`:
```lua
Config.TransactionTypes = {
    "deposit",
    "withdraw",
    "transfer",
    "interest",
    "fee",
    "loan_payment",
    "salary",
    "your_custom_type" -- Add here
}
```

## Troubleshooting

### Common Issues

1. **Banking UI not opening**
   - Check console for JavaScript errors
   - Ensure ox_target is properly installed
   - Verify resource load order

2. **Database errors**
   - Ensure oxmysql is running
   - Check database connection
   - Verify table creation

3. **ATMs not working**
   - Check ox_target configuration
   - Verify ATM model hashes in config
   - Ensure proper resource dependencies

4. **Transactions failing**
   - Check server console for errors
   - Verify player has sufficient funds
   - Check database constraints

### Debug Mode
Enable debug mode in config for detailed logging:
```lua
Config.Debug = true
```

## Support

For support and updates:
- Check the console for error messages
- Verify all dependencies are installed
- Ensure proper database setup
- Review configuration settings

## License

This resource is provided as-is for educational and development purposes.

## Credits

- QBCore Framework
- ox_target for interaction system
- oxmysql for database operations
- Font Awesome for icons
