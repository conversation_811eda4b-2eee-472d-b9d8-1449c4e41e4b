local QBCore = exports['qb-core']:GetCoreObject()

-- Initialize Database
CreateThread(function()
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `bank_accounts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `citizenid` varchar(50) NOT NULL,
            `account_number` varchar(20) NOT NULL UNIQUE,
            `account_type` varchar(20) DEFAULT 'checking',
            `balance` decimal(15,2) DEFAULT 0.00,
            `pin` varchar(4) DEFAULT '0000',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `citizenid` (`citizenid`),
            KEY `account_number` (`account_number`)
        )
    ]])
    
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `bank_transactions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `account_number` varchar(20) NOT NULL,
            `transaction_type` varchar(20) NOT NULL,
            `amount` decimal(15,2) NOT NULL,
            `balance_after` decimal(15,2) NOT NULL,
            `description` text,
            `target_account` varchar(20) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `account_number` (`account_number`),
            KEY `created_at` (`created_at`)
        )
    ]])
    
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `bank_loans` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `citizenid` varchar(50) NOT NULL,
            `account_number` varchar(20) NOT NULL,
            `loan_amount` decimal(15,2) NOT NULL,
            `remaining_balance` decimal(15,2) NOT NULL,
            `interest_rate` decimal(5,4) NOT NULL,
            `term_days` int(11) NOT NULL,
            `daily_payment` decimal(15,2) NOT NULL,
            `next_payment_date` date NOT NULL,
            `status` varchar(20) DEFAULT 'active',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `citizenid` (`citizenid`),
            KEY `account_number` (`account_number`)
        )
    ]])
end)

-- Generate Account Number
function GenerateAccountNumber()
    local accountNumber
    repeat
        accountNumber = string.format("%08d", math.random(********, ********))
        local result = MySQL.scalar.await('SELECT account_number FROM bank_accounts WHERE account_number = ?', {accountNumber})
    until not result
    return accountNumber
end

-- Get or Create Account
function GetOrCreateAccount(citizenid)
    local account = MySQL.single.await('SELECT * FROM bank_accounts WHERE citizenid = ?', {citizenid})
    
    if not account then
        local accountNumber = GenerateAccountNumber()
        MySQL.insert.await('INSERT INTO bank_accounts (citizenid, account_number, account_type, balance) VALUES (?, ?, ?, ?)', {
            citizenid, accountNumber, 'checking', 0.00
        })
        account = MySQL.single.await('SELECT * FROM bank_accounts WHERE citizenid = ?', {citizenid})
    end
    
    return account
end

-- Add Transaction Record
function AddTransaction(accountNumber, transactionType, amount, balanceAfter, description, targetAccount)
    MySQL.insert('INSERT INTO bank_transactions (account_number, transaction_type, amount, balance_after, description, target_account) VALUES (?, ?, ?, ?, ?, ?)', {
        accountNumber, transactionType, amount, balanceAfter, description, targetAccount
    })
end

-- QBCore Callbacks
QBCore.Functions.CreateCallback('qb-banking:server:GetAccountData', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb(nil) end
    
    local account = GetOrCreateAccount(Player.PlayerData.citizenid)
    local loan = MySQL.single.await('SELECT * FROM bank_loans WHERE citizenid = ? AND status = ?', {
        Player.PlayerData.citizenid, 'active'
    })
    
    cb({
        account = account,
        loan = loan,
        cash = Player.PlayerData.money.cash
    })
end)

QBCore.Functions.CreateCallback('qb-banking:server:Deposit', function(source, cb, amount)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb(false, Locale.error.account_not_found) end
    
    local cash = Player.PlayerData.money.cash
    if cash < amount then
        return cb(false, Locale.error.not_enough_money)
    end
    
    local account = GetOrCreateAccount(Player.PlayerData.citizenid)
    local newBalance = account.balance + amount
    
    -- Remove cash from player
    Player.Functions.RemoveMoney('cash', amount, 'bank-deposit')
    
    -- Update bank balance
    MySQL.update.await('UPDATE bank_accounts SET balance = ? WHERE citizenid = ?', {
        newBalance, Player.PlayerData.citizenid
    })
    
    -- Add transaction record
    AddTransaction(account.account_number, 'deposit', amount, newBalance, 'Cash deposit')
    
    cb(true)
end)

QBCore.Functions.CreateCallback('qb-banking:server:Withdraw', function(source, cb, amount)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb(false, Locale.error.account_not_found) end
    
    local account = GetOrCreateAccount(Player.PlayerData.citizenid)
    if account.balance < amount then
        return cb(false, Locale.error.insufficient_funds)
    end
    
    local newBalance = account.balance - amount
    
    -- Add cash to player
    Player.Functions.AddMoney('cash', amount, 'bank-withdraw')
    
    -- Update bank balance
    MySQL.update.await('UPDATE bank_accounts SET balance = ? WHERE citizenid = ?', {
        newBalance, Player.PlayerData.citizenid
    })
    
    -- Add transaction record
    AddTransaction(account.account_number, 'withdraw', amount, newBalance, 'Cash withdrawal')
    
    cb(true)
end)

QBCore.Functions.CreateCallback('qb-banking:server:Transfer', function(source, cb, amount, targetAccountNumber, description)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb(false, Locale.error.account_not_found) end
    
    local senderAccount = GetOrCreateAccount(Player.PlayerData.citizenid)
    local targetAccount = MySQL.single.await('SELECT * FROM bank_accounts WHERE account_number = ?', {targetAccountNumber})
    
    if not targetAccount then
        return cb(false, Locale.error.invalid_account_number)
    end
    
    if senderAccount.account_number == targetAccountNumber then
        return cb(false, Locale.error.same_account)
    end
    
    local fee = math.floor(amount * Config.BankingFee)
    local totalAmount = amount + fee
    
    if senderAccount.balance < totalAmount then
        return cb(false, Locale.error.insufficient_funds)
    end
    
    if amount < Config.MinTransferAmount or amount > Config.MaxTransferAmount then
        return cb(false, Locale.error.invalid_amount)
    end
    
    -- Update sender balance
    local newSenderBalance = senderAccount.balance - totalAmount
    MySQL.update.await('UPDATE bank_accounts SET balance = ? WHERE account_number = ?', {
        newSenderBalance, senderAccount.account_number
    })
    
    -- Update target balance
    local newTargetBalance = targetAccount.balance + amount
    MySQL.update.await('UPDATE bank_accounts SET balance = ? WHERE account_number = ?', {
        newTargetBalance, targetAccount.account_number
    })
    
    -- Add transaction records
    AddTransaction(senderAccount.account_number, 'transfer', -totalAmount, newSenderBalance, 
        description or 'Transfer to ' .. targetAccountNumber, targetAccountNumber)
    AddTransaction(targetAccount.account_number, 'transfer', amount, newTargetBalance, 
        description or 'Transfer from ' .. senderAccount.account_number, senderAccount.account_number)
    
    if fee > 0 then
        AddTransaction(senderAccount.account_number, 'fee', -fee, newSenderBalance, 'Transfer fee')
    end
    
    cb(true)
end)

QBCore.Functions.CreateCallback('qb-banking:server:GetTransactionHistory', function(source, cb, page, limit)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb({}) end
    
    local account = GetOrCreateAccount(Player.PlayerData.citizenid)
    local offset = (page - 1) * limit
    
    local transactions = MySQL.query.await([[
        SELECT * FROM bank_transactions 
        WHERE account_number = ? 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ]], {account.account_number, limit, offset})
    
    cb(transactions or {})
end)

QBCore.Functions.CreateCallback('qb-banking:server:ApplyLoan', function(source, cb, amount, term)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb(false, Locale.error.account_not_found) end
    
    -- Check if player already has a loan
    local existingLoan = MySQL.single.await('SELECT * FROM bank_loans WHERE citizenid = ? AND status = ?', {
        Player.PlayerData.citizenid, 'active'
    })
    
    if existingLoan then
        return cb(false, Locale.error.already_have_loan)
    end
    
    if amount < Config.LoanSettings.minAmount or amount > Config.LoanSettings.maxAmount then
        return cb(false, Locale.error.invalid_amount)
    end
    
    if term < Config.LoanSettings.minTerm or term > Config.LoanSettings.maxTerm then
        return cb(false, Locale.error.loan_denied)
    end
    
    local account = GetOrCreateAccount(Player.PlayerData.citizenid)
    local interestRate = Config.LoanSettings.interestRate
    local totalAmount = amount * (1 + interestRate)
    local dailyPayment = totalAmount / term
    local nextPaymentDate = os.date('%Y-%m-%d', os.time() + 86400) -- Tomorrow
    
    -- Create loan record
    MySQL.insert.await([[
        INSERT INTO bank_loans (citizenid, account_number, loan_amount, remaining_balance, interest_rate, term_days, daily_payment, next_payment_date) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ]], {
        Player.PlayerData.citizenid, account.account_number, amount, totalAmount, interestRate, term, dailyPayment, nextPaymentDate
    })
    
    -- Add money to bank account
    local newBalance = account.balance + amount
    MySQL.update.await('UPDATE bank_accounts SET balance = ? WHERE citizenid = ?', {
        newBalance, Player.PlayerData.citizenid
    })
    
    -- Add transaction record
    AddTransaction(account.account_number, 'loan', amount, newBalance, 'Loan disbursement')
    
    cb(true)
end)

QBCore.Functions.CreateCallback('qb-banking:server:PayLoan', function(source, cb, amount)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb(false, Locale.error.account_not_found) end
    
    local loan = MySQL.single.await('SELECT * FROM bank_loans WHERE citizenid = ? AND status = ?', {
        Player.PlayerData.citizenid, 'active'
    })
    
    if not loan then
        return cb(false, Locale.error.account_not_found)
    end
    
    local account = GetOrCreateAccount(Player.PlayerData.citizenid)
    if account.balance < amount then
        return cb(false, Locale.error.insufficient_funds)
    end
    
    local newBalance = account.balance - amount
    local newLoanBalance = loan.remaining_balance - amount
    
    -- Update bank balance
    MySQL.update.await('UPDATE bank_accounts SET balance = ? WHERE citizenid = ?', {
        newBalance, Player.PlayerData.citizenid
    })
    
    if newLoanBalance <= 0 then
        -- Loan paid off
        MySQL.update.await('UPDATE bank_loans SET remaining_balance = 0, status = ? WHERE id = ?', {
            'paid', loan.id
        })
    else
        -- Update loan balance
        MySQL.update.await('UPDATE bank_loans SET remaining_balance = ? WHERE id = ?', {
            newLoanBalance, loan.id
        })
    end
    
    -- Add transaction record
    AddTransaction(account.account_number, 'loan_payment', -amount, newBalance, 'Loan payment')
    
    cb(true)
end)

-- Daily Interest and Loan Payments (runs every hour)
CreateThread(function()
    while true do
        Wait(3600000) -- 1 hour
        
        -- Process daily interest (only once per day)
        local currentHour = tonumber(os.date('%H'))
        if currentHour == 0 then -- Midnight
            local accounts = MySQL.query.await('SELECT * FROM bank_accounts WHERE balance > 0')
            for _, account in pairs(accounts) do
                local accountType = Config.AccountTypes[account.account_type]
                if accountType then
                    local interest = math.floor(account.balance * accountType.interestRate)
                    if interest > 0 then
                        local newBalance = account.balance + interest
                        MySQL.update.await('UPDATE bank_accounts SET balance = ? WHERE id = ?', {
                            newBalance, account.id
                        })
                        AddTransaction(account.account_number, 'interest', interest, newBalance, 'Daily interest')
                    end
                end
            end
        end
        
        -- Process loan payments
        local currentDate = os.date('%Y-%m-%d')
        local dueLoans = MySQL.query.await('SELECT * FROM bank_loans WHERE next_payment_date <= ? AND status = ?', {
            currentDate, 'active'
        })
        
        for _, loan in pairs(dueLoans) do
            local account = MySQL.single.await('SELECT * FROM bank_accounts WHERE account_number = ?', {loan.account_number})
            if account and account.balance >= loan.daily_payment then
                -- Auto-deduct payment
                local newBalance = account.balance - loan.daily_payment
                local newLoanBalance = loan.remaining_balance - loan.daily_payment
                
                MySQL.update.await('UPDATE bank_accounts SET balance = ? WHERE account_number = ?', {
                    newBalance, account.account_number
                })
                
                if newLoanBalance <= 0 then
                    MySQL.update.await('UPDATE bank_loans SET remaining_balance = 0, status = ? WHERE id = ?', {
                        'paid', loan.id
                    })
                else
                    local nextPaymentDate = os.date('%Y-%m-%d', os.time() + 86400)
                    MySQL.update.await('UPDATE bank_loans SET remaining_balance = ?, next_payment_date = ? WHERE id = ?', {
                        newLoanBalance, nextPaymentDate, loan.id
                    })
                end
                
                AddTransaction(account.account_number, 'loan_payment', -loan.daily_payment, newBalance, 'Automatic loan payment')
            end
        end
    end
end)
