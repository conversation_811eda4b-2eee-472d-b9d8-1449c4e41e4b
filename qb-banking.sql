-- QB-Banking Database Structure
-- Execute this SQL file to create the necessary tables for the banking system

-- Create bank_accounts table
CREATE TABLE IF NOT EXISTS `bank_accounts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `account_number` varchar(20) NOT NULL UNIQUE,
    `account_type` varchar(20) DEFAULT 'checking',
    `balance` decimal(15,2) DEFAULT 0.00,
    `pin` varchar(4) DEFAULT '0000',
    `status` varchar(20) DEFAULT 'active',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `citizenid` (`citizenid`),
    KEY `account_number` (`account_number`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create bank_transactions table
CREATE TABLE IF NOT EXISTS `bank_transactions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `account_number` varchar(20) NOT NULL,
    `transaction_type` varchar(20) NOT NULL,
    `amount` decimal(15,2) NOT NULL,
    `balance_after` decimal(15,2) NOT NULL,
    `description` text,
    `target_account` varchar(20) DEFAULT NULL,
    `reference_id` varchar(50) DEFAULT NULL,
    `status` varchar(20) DEFAULT 'completed',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `account_number` (`account_number`),
    KEY `transaction_type` (`transaction_type`),
    KEY `created_at` (`created_at`),
    KEY `status` (`status`),
    FOREIGN KEY (`account_number`) REFERENCES `bank_accounts`(`account_number`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create bank_loans table
CREATE TABLE IF NOT EXISTS `bank_loans` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `account_number` varchar(20) NOT NULL,
    `loan_amount` decimal(15,2) NOT NULL,
    `remaining_balance` decimal(15,2) NOT NULL,
    `interest_rate` decimal(5,4) NOT NULL,
    `term_days` int(11) NOT NULL,
    `daily_payment` decimal(15,2) NOT NULL,
    `next_payment_date` date NOT NULL,
    `payments_made` int(11) DEFAULT 0,
    `status` varchar(20) DEFAULT 'active',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `citizenid` (`citizenid`),
    KEY `account_number` (`account_number`),
    KEY `status` (`status`),
    KEY `next_payment_date` (`next_payment_date`),
    FOREIGN KEY (`account_number`) REFERENCES `bank_accounts`(`account_number`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create bank_cards table (for future ATM card functionality)
CREATE TABLE IF NOT EXISTS `bank_cards` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `account_number` varchar(20) NOT NULL,
    `card_number` varchar(16) NOT NULL UNIQUE,
    `card_type` varchar(20) DEFAULT 'debit',
    `pin` varchar(4) NOT NULL,
    `expiry_date` date NOT NULL,
    `cvv` varchar(3) NOT NULL,
    `status` varchar(20) DEFAULT 'active',
    `daily_limit` decimal(15,2) DEFAULT 5000.00,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `citizenid` (`citizenid`),
    KEY `account_number` (`account_number`),
    KEY `card_number` (`card_number`),
    KEY `status` (`status`),
    FOREIGN KEY (`account_number`) REFERENCES `bank_accounts`(`account_number`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create bank_notifications table
CREATE TABLE IF NOT EXISTS `bank_notifications` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `account_number` varchar(20) NOT NULL,
    `title` varchar(100) NOT NULL,
    `message` text NOT NULL,
    `type` varchar(20) DEFAULT 'info',
    `is_read` tinyint(1) DEFAULT 0,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `citizenid` (`citizenid`),
    KEY `account_number` (`account_number`),
    KEY `is_read` (`is_read`),
    KEY `created_at` (`created_at`),
    FOREIGN KEY (`account_number`) REFERENCES `bank_accounts`(`account_number`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create bank_settings table
CREATE TABLE IF NOT EXISTS `bank_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `account_number` varchar(20) NOT NULL,
    `setting_key` varchar(50) NOT NULL,
    `setting_value` text NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_setting` (`citizenid`, `account_number`, `setting_key`),
    KEY `citizenid` (`citizenid`),
    KEY `account_number` (`account_number`),
    FOREIGN KEY (`account_number`) REFERENCES `bank_accounts`(`account_number`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default account types (optional)
INSERT IGNORE INTO `bank_settings` (`citizenid`, `account_number`, `setting_key`, `setting_value`) VALUES
('system', 'system', 'account_types', '{"checking":{"name":"Checking Account","interest_rate":0.0005,"min_balance":0,"max_balance":500000},"savings":{"name":"Savings Account","interest_rate":0.002,"min_balance":100,"max_balance":1000000},"business":{"name":"Business Account","interest_rate":0.001,"min_balance":1000,"max_balance":5000000}}');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_transactions_date_type` ON `bank_transactions` (`created_at`, `transaction_type`);
CREATE INDEX IF NOT EXISTS `idx_loans_payment_date` ON `bank_loans` (`next_payment_date`, `status`);
CREATE INDEX IF NOT EXISTS `idx_accounts_balance` ON `bank_accounts` (`balance`, `status`);
CREATE INDEX IF NOT EXISTS `idx_notifications_unread` ON `bank_notifications` (`citizenid`, `is_read`);

-- Create views for common queries
CREATE OR REPLACE VIEW `v_account_summary` AS
SELECT 
    ba.citizenid,
    ba.account_number,
    ba.account_type,
    ba.balance,
    ba.status,
    COALESCE(bl.remaining_balance, 0) as loan_balance,
    bl.next_payment_date,
    bl.daily_payment,
    (SELECT COUNT(*) FROM bank_notifications bn WHERE bn.citizenid = ba.citizenid AND bn.is_read = 0) as unread_notifications
FROM bank_accounts ba
LEFT JOIN bank_loans bl ON ba.account_number = bl.account_number AND bl.status = 'active'
WHERE ba.status = 'active';

-- Create stored procedures for common operations
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS `sp_transfer_funds`(
    IN p_from_account VARCHAR(20),
    IN p_to_account VARCHAR(20),
    IN p_amount DECIMAL(15,2),
    IN p_description TEXT,
    OUT p_result VARCHAR(20)
)
BEGIN
    DECLARE v_from_balance DECIMAL(15,2);
    DECLARE v_to_balance DECIMAL(15,2);
    DECLARE v_new_from_balance DECIMAL(15,2);
    DECLARE v_new_to_balance DECIMAL(15,2);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = 'ERROR';
    END;

    START TRANSACTION;
    
    -- Get current balances
    SELECT balance INTO v_from_balance FROM bank_accounts WHERE account_number = p_from_account AND status = 'active' FOR UPDATE;
    SELECT balance INTO v_to_balance FROM bank_accounts WHERE account_number = p_to_account AND status = 'active' FOR UPDATE;
    
    -- Check if accounts exist and have sufficient funds
    IF v_from_balance IS NULL OR v_to_balance IS NULL THEN
        SET p_result = 'INVALID_ACCOUNT';
        ROLLBACK;
    ELSEIF v_from_balance < p_amount THEN
        SET p_result = 'INSUFFICIENT_FUNDS';
        ROLLBACK;
    ELSE
        -- Calculate new balances
        SET v_new_from_balance = v_from_balance - p_amount;
        SET v_new_to_balance = v_to_balance + p_amount;
        
        -- Update balances
        UPDATE bank_accounts SET balance = v_new_from_balance WHERE account_number = p_from_account;
        UPDATE bank_accounts SET balance = v_new_to_balance WHERE account_number = p_to_account;
        
        -- Insert transaction records
        INSERT INTO bank_transactions (account_number, transaction_type, amount, balance_after, description, target_account)
        VALUES (p_from_account, 'transfer', -p_amount, v_new_from_balance, p_description, p_to_account);
        
        INSERT INTO bank_transactions (account_number, transaction_type, amount, balance_after, description, target_account)
        VALUES (p_to_account, 'transfer', p_amount, v_new_to_balance, p_description, p_from_account);
        
        SET p_result = 'SUCCESS';
        COMMIT;
    END IF;
END //

DELIMITER ;

-- Create triggers for automatic notifications
DELIMITER //

CREATE TRIGGER IF NOT EXISTS `tr_low_balance_notification` 
AFTER UPDATE ON `bank_accounts`
FOR EACH ROW
BEGIN
    IF NEW.balance < 100 AND OLD.balance >= 100 THEN
        INSERT INTO bank_notifications (citizenid, account_number, title, message, type)
        VALUES (NEW.citizenid, NEW.account_number, 'Low Balance Warning', 
                CONCAT('Your account balance is now $', NEW.balance, '. Please consider making a deposit.'), 'warning');
    END IF;
END //

CREATE TRIGGER IF NOT EXISTS `tr_large_transaction_notification`
AFTER INSERT ON `bank_transactions`
FOR EACH ROW
BEGIN
    IF ABS(NEW.amount) >= 10000 THEN
        INSERT INTO bank_notifications (citizenid, account_number, title, message, type)
        SELECT ba.citizenid, NEW.account_number, 'Large Transaction Alert',
               CONCAT('A ', NEW.transaction_type, ' of $', ABS(NEW.amount), ' was processed on your account.'), 'info'
        FROM bank_accounts ba WHERE ba.account_number = NEW.account_number;
    END IF;
END //

DELIMITER ;
