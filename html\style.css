/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: #ffffff;
    overflow: hidden;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Main Container */
#banking-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.banking-app {
    width: 90vw;
    max-width: 1200px;
    height: 85vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Header */
.header {
    background: linear-gradient(90deg, #0f3460 0%, #16537e 100%);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-left i {
    font-size: 24px;
    color: #64b5f6;
}

.header-left h1 {
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

#player-name {
    font-size: 16px;
    color: #b0bec5;
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: #ffffff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: rgba(0, 0, 0, 0.3);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 0;
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.nav-item {
    background: none;
    border: none;
    color: #b0bec5;
    padding: 15px 25px;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    font-size: 16px;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
}

.nav-item.active {
    background: linear-gradient(90deg, rgba(100, 181, 246, 0.2) 0%, transparent 100%);
    color: #64b5f6;
    border-right: 3px solid #64b5f6;
}

.nav-item i {
    font-size: 18px;
    width: 20px;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #ffffff;
}

#account-number {
    font-size: 14px;
    color: #64b5f6;
    background: rgba(100, 181, 246, 0.1);
    padding: 8px 16px;
    border-radius: 20px;
}

/* Balance Cards */
.balance-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.balance-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
}

.balance-card.primary {
    background: linear-gradient(135deg, rgba(100, 181, 246, 0.2) 0%, rgba(100, 181, 246, 0.1) 100%);
    border-color: rgba(100, 181, 246, 0.3);
}

.balance-card.secondary {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(76, 175, 80, 0.1) 100%);
    border-color: rgba(76, 175, 80, 0.3);
}

.balance-card.tertiary {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.2) 0%, rgba(255, 152, 0, 0.1) 100%);
    border-color: rgba(255, 152, 0, 0.3);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #b0bec5;
    font-size: 14px;
}

.card-header i {
    font-size: 18px;
}

.card-amount {
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h3 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #ffffff;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    background: linear-gradient(135deg, rgba(100, 181, 246, 0.2) 0%, rgba(100, 181, 246, 0.1) 100%);
    border: 1px solid rgba(100, 181, 246, 0.3);
    color: #64b5f6;
    padding: 15px 25px;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    font-size: 16px;
}

.action-btn:hover {
    background: linear-gradient(135deg, rgba(100, 181, 246, 0.3) 0%, rgba(100, 181, 246, 0.2) 100%);
    transform: translateY(-2px);
}

.deposit-btn {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(76, 175, 80, 0.1) 100%);
    border-color: rgba(76, 175, 80, 0.3);
    color: #4caf50;
}

.deposit-btn:hover {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.3) 0%, rgba(76, 175, 80, 0.2) 100%);
}

.withdraw-btn {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.2) 0%, rgba(244, 67, 54, 0.1) 100%);
    border-color: rgba(244, 67, 54, 0.3);
    color: #f44336;
}

.withdraw-btn:hover {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.3) 0%, rgba(244, 67, 54, 0.2) 100%);
}

/* Forms */
.transaction-forms,
.transfer-form,
.loan-application {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 30px;
    backdrop-filter: blur(10px);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #b0bec5;
    font-size: 14px;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #64b5f6;
    background: rgba(255, 255, 255, 0.15);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-prefix {
    position: absolute;
    left: 16px;
    color: #64b5f6;
    font-weight: 600;
    z-index: 1;
}

.input-group input {
    padding-left: 40px;
}

.form-group small {
    color: #78909c;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* Buttons */
.btn {
    background: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
    border: none;
    color: #ffffff;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    justify-content: center;
}

.btn:hover {
    background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(100, 181, 246, 0.3);
}

.btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    color: #78909c;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #b0bec5;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    color: #ffffff;
}

.btn-success {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.btn-success:hover {
    background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

/* Transfer Info */
.transfer-info {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    color: #b0bec5;
}

.info-row.total {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 10px;
    margin-top: 10px;
    font-weight: 600;
    color: #ffffff;
}

/* Loan Details */
.loan-details,
.loan-status {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.detail-row,
.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    color: #b0bec5;
}

.detail-row.total {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 10px;
    margin-top: 10px;
    font-weight: 600;
    color: #ffffff;
}

/* Transaction History */
.history-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.history-filters select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px 15px;
    color: #ffffff;
    min-width: 200px;
}

.transaction-list {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    min-height: 400px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.transaction-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.transaction-type {
    font-weight: 600;
    color: #ffffff;
    text-transform: capitalize;
}

.transaction-description {
    color: #78909c;
    font-size: 14px;
}

.transaction-date {
    color: #78909c;
    font-size: 12px;
}

.transaction-amount {
    font-weight: 600;
    font-size: 18px;
}

.transaction-amount.positive {
    color: #4caf50;
}

.transaction-amount.negative {
    color: #f44336;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px;
    color: #78909c;
    gap: 15px;
}

.loading i {
    font-size: 32px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
}

#page-info {
    color: #b0bec5;
    font-size: 14px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    color: #64b5f6;
}

.loading-spinner i {
    font-size: 48px;
}

.loading-spinner span {
    font-size: 18px;
    font-weight: 500;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3000;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(90deg, #0f3460 0%, #16537e 100%);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
}

.modal-body {
    padding: 25px;
}

.modal-body p {
    color: #b0bec5;
    margin-bottom: 20px;
    line-height: 1.6;
}

.modal-footer {
    padding: 20px 25px;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(100, 181, 246, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 181, 246, 0.7);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.section {
    animation: fadeIn 0.3s ease-in-out;
}

.transaction-item {
    animation: slideIn 0.3s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banking-app {
        width: 95vw;
        height: 95vh;
    }

    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .nav-menu {
        flex-direction: row;
        overflow-x: auto;
        padding: 0 20px;
    }

    .nav-item {
        min-width: 120px;
        justify-content: center;
        text-align: center;
        flex-direction: column;
        gap: 5px;
        padding: 15px 10px;
    }

    .nav-item span {
        font-size: 12px;
    }

    .content-area {
        padding: 20px;
    }

    .balance-cards {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        justify-content: center;
    }

    .history-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .pagination {
        flex-direction: column;
        gap: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-footer {
        flex-direction: column;
    }
}
