Config = {}

-- Banking Settings
Config.BankingFee = 0.02 -- 2% fee for transfers
Config.MaxTransferAmount = 100000 -- Maximum transfer amount
Config.MinTransferAmount = 1 -- Minimum transfer amount
Config.InterestRate = 0.001 -- Daily interest rate (0.1%)
Config.MaxSavingsAmount = 1000000 -- Maximum savings account balance

-- ATM Locations
Config.ATMModels = {
    `prop_atm_01`,
    `prop_atm_02`,
    `prop_atm_03`,
    `prop_fleeca_atm`
}

-- Bank Locations
Config.BankLocations = {
    {
        name = "Fleeca Bank",
        coords = vector3(149.9, -1040.2, 29.37),
        blip = true,
        ped = {
            model = "a_m_m_business_01",
            coords = vector4(149.46, -1040.73, 28.37, 340.0),
            scenario = "WORLD_HUMAN_STAND_IMPATIENT"
        }
    },
    {
        name = "Fleeca Bank",
        coords = vector3(314.187, -278.621, 54.170),
        blip = true,
        ped = {
            model = "a_f_y_business_02",
            coords = vector4(313.84, -279.40, 53.17, 340.0),
            scenario = "WORLD_HUMAN_CLIPBOARD"
        }
    },
    {
        name = "Fleeca Bank",
        coords = vector3(-2962.582, 482.627, 15.703),
        blip = true,
        ped = {
            model = "a_m_m_business_01",
            coords = vector4(-2961.95, 481.68, 14.70, 358.0),
            scenario = "WORLD_HUMAN_STAND_IMPATIENT"
        }
    },
    {
        name = "Fleeca Bank",
        coords = vector3(-112.202, 6469.295, 31.626),
        blip = true,
        ped = {
            model = "a_f_y_business_02",
            coords = vector4(-111.22, 6469.91, 30.63, 136.0),
            scenario = "WORLD_HUMAN_CLIPBOARD"
        }
    },
    {
        name = "Fleeca Bank",
        coords = vector3(1175.**********, 2706.**********, 38.************),
        blip = true,
        ped = {
            model = "a_m_m_business_01",
            coords = vector4(1174.532, 2707.278, 37.09, 180.0),
            scenario = "WORLD_HUMAN_STAND_IMPATIENT"
        }
    },
    {
        name = "Pacific Standard Bank",
        coords = vector3(248.42, 225.366, 106.287),
        blip = true,
        ped = {
            model = "a_f_y_business_04",
            coords = vector4(248.64, 224.10, 105.29, 160.0),
            scenario = "WORLD_HUMAN_CLIPBOARD"
        }
    }
}

-- Blip Settings
Config.Blips = {
    {
        name = "Bank",
        sprite = 108,
        color = 2,
        scale = 0.8
    },
    {
        name = "ATM",
        sprite = 277,
        color = 2,
        scale = 0.6
    }
}

-- Account Types
Config.AccountTypes = {
    checking = {
        name = "Checking Account",
        interestRate = 0.0005,
        minBalance = 0,
        maxBalance = 500000
    },
    savings = {
        name = "Savings Account",
        interestRate = 0.002,
        minBalance = 100,
        maxBalance = 1000000
    },
    business = {
        name = "Business Account",
        interestRate = 0.001,
        minBalance = 1000,
        maxBalance = 5000000
    }
}

-- Transaction Types
Config.TransactionTypes = {
    "deposit",
    "withdraw",
    "transfer",
    "interest",
    "fee",
    "loan_payment",
    "salary"
}

-- Loan Settings
Config.LoanSettings = {
    maxAmount = 50000,
    minAmount = 1000,
    interestRate = 0.05, -- 5% interest
    maxTerm = 30, -- 30 days
    minTerm = 7 -- 7 days
}
