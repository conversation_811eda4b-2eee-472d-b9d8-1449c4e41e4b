// Global Variables
let accountData = null;
let currentPage = 1;
let isATM = false;

// DOM Elements
const bankingContainer = document.getElementById('banking-container');
const loadingOverlay = document.getElementById('loading-overlay');
const confirmationModal = document.getElementById('confirmation-modal');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
});

// Setup Event Listeners
function setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function() {
            const section = this.dataset.section;
            switchSection(section);
        });
    });

    // Close button
    document.getElementById('close-btn').addEventListener('click', closeBank);

    // Quick actions
    document.getElementById('quick-deposit').addEventListener('click', () => switchSection('transactions'));
    document.getElementById('quick-withdraw').addEventListener('click', () => {
        switchSection('transactions');
        document.getElementById('transaction-type').value = 'withdraw';
    });
    document.getElementById('quick-transfer').addEventListener('click', () => switchSection('transfer'));

    // Transaction form
    document.getElementById('process-transaction').addEventListener('click', processTransaction);

    // Transfer form
    document.getElementById('transfer-amount').addEventListener('input', calculateTransferFee);
    document.getElementById('process-transfer').addEventListener('click', processTransfer);

    // Loan form
    document.getElementById('loan-amount').addEventListener('input', calculateLoanDetails);
    document.getElementById('loan-term').addEventListener('change', calculateLoanDetails);
    document.getElementById('apply-loan').addEventListener('click', applyForLoan);
    document.getElementById('make-payment').addEventListener('click', makeLoanPayment);

    // History
    document.getElementById('refresh-history').addEventListener('click', loadTransactionHistory);
    document.getElementById('prev-page').addEventListener('click', () => changePage(-1));
    document.getElementById('next-page').addEventListener('click', () => changePage(1));

    // Modal
    document.getElementById('modal-close').addEventListener('click', closeModal);
    document.getElementById('modal-cancel').addEventListener('click', closeModal);

    // ESC key to close
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (!confirmationModal.classList.contains('hidden')) {
                closeModal();
            } else {
                closeBank();
            }
        }
    });
}

// NUI Message Handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openBank':
            openBank(data.accountData, data.isATM, data.playerName);
            break;
        case 'closeBank':
            closeBank();
            break;
        case 'updateAccountData':
            updateAccountData(data.accountData);
            break;
    }
});

// Open Bank
function openBank(data, atmMode, playerName) {
    accountData = data;
    isATM = atmMode;
    
    document.getElementById('player-name').textContent = playerName;
    
    // Hide loan section for ATM
    if (isATM) {
        document.getElementById('loans-nav').style.display = 'none';
    }
    
    updateUI();
    bankingContainer.classList.remove('hidden');
}

// Close Bank
function closeBank() {
    bankingContainer.classList.add('hidden');
    fetch(`https://${GetParentResourceName()}/closeBank`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Update UI with account data
function updateUI() {
    if (!accountData) return;

    // Update account info
    document.getElementById('account-number').textContent = `Account: #${accountData.account.account_number}`;
    document.getElementById('bank-balance').textContent = formatCurrency(accountData.account.balance);
    document.getElementById('cash-balance').textContent = formatCurrency(accountData.cash);

    // Update loan info
    if (accountData.loan) {
        document.getElementById('loan-card').style.display = 'block';
        document.getElementById('loan-balance').textContent = formatCurrency(accountData.loan.remaining_balance);
        
        document.getElementById('no-loan-content').style.display = 'none';
        document.getElementById('active-loan-content').style.display = 'block';
        
        document.getElementById('remaining-balance').textContent = formatCurrency(accountData.loan.remaining_balance);
        document.getElementById('current-daily-payment').textContent = formatCurrency(accountData.loan.daily_payment);
        document.getElementById('next-payment-date').textContent = formatDate(accountData.loan.next_payment_date);
    } else {
        document.getElementById('loan-card').style.display = 'none';
        document.getElementById('no-loan-content').style.display = 'block';
        document.getElementById('active-loan-content').style.display = 'none';
    }
}

// Update Account Data
function updateAccountData(data) {
    accountData = data;
    updateUI();
}

// Switch Section
function switchSection(sectionName) {
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

    // Update content
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById(`${sectionName}-section`).classList.add('active');

    // Load data for specific sections
    if (sectionName === 'history') {
        loadTransactionHistory();
    }
}

// Process Transaction (Deposit/Withdraw)
function processTransaction() {
    const type = document.getElementById('transaction-type').value;
    const amount = parseFloat(document.getElementById('transaction-amount').value);

    if (!amount || amount <= 0) {
        showNotification('Please enter a valid amount', 'error');
        return;
    }

    if (type === 'withdraw' && amount > accountData.account.balance) {
        showNotification('Insufficient funds', 'error');
        return;
    }

    if (type === 'deposit' && amount > accountData.cash) {
        showNotification('Not enough cash', 'error');
        return;
    }

    showConfirmation(
        `Confirm ${type.charAt(0).toUpperCase() + type.slice(1)}`,
        `Are you sure you want to ${type} ${formatCurrency(amount)}?`,
        () => {
            showLoading(true);
            fetch(`https://${GetParentResourceName()}/${type}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ amount: amount })
            })
            .then(response => response.text())
            .then(result => {
                showLoading(false);
                if (result === 'success') {
                    document.getElementById('transaction-amount').value = '';
                    showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} successful`, 'success');
                } else {
                    showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} failed`, 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showNotification('Transaction failed', 'error');
            });
        }
    );
}

// Calculate Transfer Fee
function calculateTransferFee() {
    const amount = parseFloat(document.getElementById('transfer-amount').value) || 0;
    const fee = Math.floor(amount * 0.02); // 2% fee
    const total = amount + fee;

    document.getElementById('transfer-fee').textContent = formatCurrency(fee);
    document.getElementById('transfer-total').textContent = formatCurrency(total);
}

// Process Transfer
function processTransfer() {
    const amount = parseFloat(document.getElementById('transfer-amount').value);
    const targetAccount = document.getElementById('transfer-account').value.trim();
    const description = document.getElementById('transfer-description').value.trim();

    if (!amount || amount <= 0) {
        showNotification('Please enter a valid amount', 'error');
        return;
    }

    if (!targetAccount) {
        showNotification('Please enter a recipient account number', 'error');
        return;
    }

    if (targetAccount === accountData.account.account_number) {
        showNotification('Cannot transfer to the same account', 'error');
        return;
    }

    const fee = Math.floor(amount * 0.02);
    const total = amount + fee;

    if (total > accountData.account.balance) {
        showNotification('Insufficient funds (including transfer fee)', 'error');
        return;
    }

    showConfirmation(
        'Confirm Transfer',
        `Transfer ${formatCurrency(amount)} to account ${targetAccount}?<br>Fee: ${formatCurrency(fee)}<br>Total: ${formatCurrency(total)}`,
        () => {
            showLoading(true);
            fetch(`https://${GetParentResourceName()}/transfer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    amount: amount,
                    targetAccount: targetAccount,
                    description: description
                })
            })
            .then(response => response.text())
            .then(result => {
                showLoading(false);
                if (result === 'success') {
                    document.getElementById('transfer-amount').value = '';
                    document.getElementById('transfer-account').value = '';
                    document.getElementById('transfer-description').value = '';
                    calculateTransferFee();
                    showNotification('Transfer successful', 'success');
                } else {
                    showNotification('Transfer failed', 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showNotification('Transfer failed', 'error');
            });
        }
    );
}

// Calculate Loan Details
function calculateLoanDetails() {
    const amount = parseFloat(document.getElementById('loan-amount').value) || 0;
    const term = parseInt(document.getElementById('loan-term').value) || 7;
    const interestRate = 0.05; // 5%
    
    const totalAmount = amount * (1 + interestRate);
    const dailyPayment = totalAmount / term;

    document.getElementById('daily-payment').textContent = formatCurrency(dailyPayment);
    document.getElementById('total-repayment').textContent = formatCurrency(totalAmount);
}

// Apply for Loan
function applyForLoan() {
    const amount = parseFloat(document.getElementById('loan-amount').value);
    const term = parseInt(document.getElementById('loan-term').value);

    if (!amount || amount < 1000 || amount > 50000) {
        showNotification('Loan amount must be between $1,000 and $50,000', 'error');
        return;
    }

    showConfirmation(
        'Confirm Loan Application',
        `Apply for a loan of ${formatCurrency(amount)} for ${term} days?`,
        () => {
            showLoading(true);
            fetch(`https://${GetParentResourceName()}/applyLoan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    amount: amount,
                    term: term
                })
            })
            .then(response => response.text())
            .then(result => {
                showLoading(false);
                if (result === 'success') {
                    showNotification('Loan approved!', 'success');
                } else {
                    showNotification('Loan application denied', 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showNotification('Loan application failed', 'error');
            });
        }
    );
}

// Make Loan Payment
function makeLoanPayment() {
    const amount = parseFloat(document.getElementById('payment-amount').value);

    if (!amount || amount <= 0) {
        showNotification('Please enter a valid payment amount', 'error');
        return;
    }

    if (amount > accountData.account.balance) {
        showNotification('Insufficient funds', 'error');
        return;
    }

    showConfirmation(
        'Confirm Loan Payment',
        `Make a loan payment of ${formatCurrency(amount)}?`,
        () => {
            showLoading(true);
            fetch(`https://${GetParentResourceName()}/payLoan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ amount: amount })
            })
            .then(response => response.text())
            .then(result => {
                showLoading(false);
                if (result === 'success') {
                    document.getElementById('payment-amount').value = '';
                    showNotification('Payment successful', 'success');
                } else {
                    showNotification('Payment failed', 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showNotification('Payment failed', 'error');
            });
        }
    );
}

// Load Transaction History
function loadTransactionHistory() {
    const transactionList = document.getElementById('transaction-list');
    transactionList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i><span>Loading transactions...</span></div>';

    fetch(`https://${GetParentResourceName()}/getTransactionHistory`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            page: currentPage,
            limit: 10
        })
    })
    .then(response => response.json())
    .then(transactions => {
        displayTransactions(transactions);
    })
    .catch(error => {
        transactionList.innerHTML = '<div class="loading"><i class="fas fa-exclamation-triangle"></i><span>Failed to load transactions</span></div>';
    });
}

// Display Transactions
function displayTransactions(transactions) {
    const transactionList = document.getElementById('transaction-list');

    if (!transactions || transactions.length === 0) {
        transactionList.innerHTML = '<div class="loading"><i class="fas fa-inbox"></i><span>No transactions found</span></div>';
        return;
    }

    let html = '';
    transactions.forEach(transaction => {
        const isPositive = transaction.amount > 0;
        const amountClass = isPositive ? 'positive' : 'negative';
        const amountPrefix = isPositive ? '+' : '';

        html += `
            <div class="transaction-item">
                <div class="transaction-info">
                    <div class="transaction-type">${transaction.transaction_type.replace('_', ' ')}</div>
                    <div class="transaction-description">${transaction.description || 'No description'}</div>
                    <div class="transaction-date">${formatDate(transaction.created_at)}</div>
                </div>
                <div class="transaction-amount ${amountClass}">
                    ${amountPrefix}${formatCurrency(Math.abs(transaction.amount))}
                </div>
            </div>
        `;
    });

    transactionList.innerHTML = html;

    // Update pagination
    document.getElementById('page-info').textContent = `Page ${currentPage}`;
    document.getElementById('prev-page').disabled = currentPage <= 1;
    document.getElementById('next-page').disabled = transactions.length < 10;
}

// Change Page
function changePage(direction) {
    const newPage = currentPage + direction;
    if (newPage < 1) return;

    currentPage = newPage;
    loadTransactionHistory();
}

// Show Loading
function showLoading(show) {
    if (show) {
        loadingOverlay.classList.remove('hidden');
    } else {
        loadingOverlay.classList.add('hidden');
    }
}

// Show Confirmation Modal
function showConfirmation(title, message, onConfirm) {
    document.getElementById('modal-title').textContent = title;
    document.getElementById('modal-message').innerHTML = message;

    const confirmBtn = document.getElementById('modal-confirm');
    confirmBtn.onclick = () => {
        closeModal();
        onConfirm();
    };

    confirmationModal.classList.remove('hidden');
}

// Close Modal
function closeModal() {
    confirmationModal.classList.add('hidden');
}

// Show Notification (placeholder - you might want to implement a proper notification system)
function showNotification(message, type) {
    console.log(`${type.toUpperCase()}: ${message}`);
    // You can implement a proper notification system here
}

// Utility Functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function GetParentResourceName() {
    return window.location.hostname === '' ? 'qb-banking' : window.location.hostname;
}
