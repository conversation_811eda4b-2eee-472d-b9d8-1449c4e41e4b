<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QB Banking System</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="banking-container" class="hidden">
        <div class="banking-app">
            <!-- Header -->
            <div class="header">
                <div class="header-left">
                    <i class="fas fa-university"></i>
                    <h1>QB Banking</h1>
                </div>
                <div class="header-right">
                    <span id="player-name">John <PERSON></span>
                    <button id="close-btn" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Sidebar -->
                <div class="sidebar">
                    <div class="nav-menu">
                        <button class="nav-item active" data-section="overview">
                            <i class="fas fa-chart-line"></i>
                            <span>Overview</span>
                        </button>
                        <button class="nav-item" data-section="transactions">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Transactions</span>
                        </button>
                        <button class="nav-item" data-section="transfer">
                            <i class="fas fa-paper-plane"></i>
                            <span>Transfer</span>
                        </button>
                        <button class="nav-item" data-section="loans" id="loans-nav">
                            <i class="fas fa-hand-holding-usd"></i>
                            <span>Loans</span>
                        </button>
                        <button class="nav-item" data-section="history">
                            <i class="fas fa-history"></i>
                            <span>History</span>
                        </button>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="content-area">
                    <!-- Overview Section -->
                    <div id="overview-section" class="section active">
                        <div class="section-header">
                            <h2>Account Overview</h2>
                            <div class="account-info">
                                <span id="account-number">Account: #********</span>
                            </div>
                        </div>

                        <div class="balance-cards">
                            <div class="balance-card primary">
                                <div class="card-header">
                                    <i class="fas fa-wallet"></i>
                                    <span>Bank Balance</span>
                                </div>
                                <div class="card-amount" id="bank-balance">$0.00</div>
                            </div>
                            <div class="balance-card secondary">
                                <div class="card-header">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>Cash on Hand</span>
                                </div>
                                <div class="card-amount" id="cash-balance">$0.00</div>
                            </div>
                            <div class="balance-card tertiary" id="loan-card" style="display: none;">
                                <div class="card-header">
                                    <i class="fas fa-credit-card"></i>
                                    <span>Outstanding Loan</span>
                                </div>
                                <div class="card-amount" id="loan-balance">$0.00</div>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <h3>Quick Actions</h3>
                            <div class="action-buttons">
                                <button class="action-btn deposit-btn" id="quick-deposit">
                                    <i class="fas fa-plus"></i>
                                    <span>Deposit</span>
                                </button>
                                <button class="action-btn withdraw-btn" id="quick-withdraw">
                                    <i class="fas fa-minus"></i>
                                    <span>Withdraw</span>
                                </button>
                                <button class="action-btn transfer-btn" id="quick-transfer">
                                    <i class="fas fa-exchange-alt"></i>
                                    <span>Transfer</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Transactions Section -->
                    <div id="transactions-section" class="section">
                        <div class="section-header">
                            <h2>Transactions</h2>
                        </div>

                        <div class="transaction-forms">
                            <div class="form-group">
                                <label>Transaction Type</label>
                                <select id="transaction-type">
                                    <option value="deposit">Deposit Cash</option>
                                    <option value="withdraw">Withdraw Cash</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Amount</label>
                                <div class="input-group">
                                    <span class="input-prefix">$</span>
                                    <input type="number" id="transaction-amount" placeholder="0.00" min="1" step="0.01">
                                </div>
                            </div>
                            <button id="process-transaction" class="btn btn-primary">
                                <i class="fas fa-check"></i>
                                Process Transaction
                            </button>
                        </div>
                    </div>

                    <!-- Transfer Section -->
                    <div id="transfer-section" class="section">
                        <div class="section-header">
                            <h2>Transfer Money</h2>
                        </div>

                        <div class="transfer-form">
                            <div class="form-group">
                                <label>Recipient Account Number</label>
                                <input type="text" id="transfer-account" placeholder="Enter account number">
                            </div>
                            <div class="form-group">
                                <label>Amount</label>
                                <div class="input-group">
                                    <span class="input-prefix">$</span>
                                    <input type="number" id="transfer-amount" placeholder="0.00" min="1" step="0.01">
                                </div>
                            </div>
                            <div class="form-group">
                                <label>Description (Optional)</label>
                                <input type="text" id="transfer-description" placeholder="What's this for?">
                            </div>
                            <div class="transfer-info">
                                <div class="info-row">
                                    <span>Transfer Fee:</span>
                                    <span id="transfer-fee">$0.00</span>
                                </div>
                                <div class="info-row total">
                                    <span>Total Amount:</span>
                                    <span id="transfer-total">$0.00</span>
                                </div>
                            </div>
                            <button id="process-transfer" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                Send Transfer
                            </button>
                        </div>
                    </div>

                    <!-- Loans Section -->
                    <div id="loans-section" class="section">
                        <div class="section-header">
                            <h2>Loan Services</h2>
                        </div>

                        <div id="no-loan-content">
                            <div class="loan-application">
                                <h3>Apply for a Loan</h3>
                                <div class="form-group">
                                    <label>Loan Amount</label>
                                    <div class="input-group">
                                        <span class="input-prefix">$</span>
                                        <input type="number" id="loan-amount" placeholder="0.00" min="1000" max="50000" step="100">
                                    </div>
                                    <small>Min: $1,000 - Max: $50,000</small>
                                </div>
                                <div class="form-group">
                                    <label>Loan Term (Days)</label>
                                    <select id="loan-term">
                                        <option value="7">7 Days</option>
                                        <option value="14">14 Days</option>
                                        <option value="21">21 Days</option>
                                        <option value="30">30 Days</option>
                                    </select>
                                </div>
                                <div class="loan-details">
                                    <div class="detail-row">
                                        <span>Interest Rate:</span>
                                        <span>5.0%</span>
                                    </div>
                                    <div class="detail-row">
                                        <span>Daily Payment:</span>
                                        <span id="daily-payment">$0.00</span>
                                    </div>
                                    <div class="detail-row total">
                                        <span>Total Repayment:</span>
                                        <span id="total-repayment">$0.00</span>
                                    </div>
                                </div>
                                <button id="apply-loan" class="btn btn-primary">
                                    <i class="fas fa-file-signature"></i>
                                    Apply for Loan
                                </button>
                            </div>
                        </div>

                        <div id="active-loan-content" style="display: none;">
                            <div class="active-loan-info">
                                <h3>Active Loan</h3>
                                <div class="loan-status">
                                    <div class="status-item">
                                        <span>Remaining Balance:</span>
                                        <span id="remaining-balance">$0.00</span>
                                    </div>
                                    <div class="status-item">
                                        <span>Daily Payment:</span>
                                        <span id="current-daily-payment">$0.00</span>
                                    </div>
                                    <div class="status-item">
                                        <span>Next Payment:</span>
                                        <span id="next-payment-date">--</span>
                                    </div>
                                </div>
                                <div class="loan-payment">
                                    <div class="form-group">
                                        <label>Payment Amount</label>
                                        <div class="input-group">
                                            <span class="input-prefix">$</span>
                                            <input type="number" id="payment-amount" placeholder="0.00" min="1" step="0.01">
                                        </div>
                                    </div>
                                    <button id="make-payment" class="btn btn-success">
                                        <i class="fas fa-credit-card"></i>
                                        Make Payment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- History Section -->
                    <div id="history-section" class="section">
                        <div class="section-header">
                            <h2>Transaction History</h2>
                        </div>

                        <div class="history-content">
                            <div class="history-filters">
                                <select id="history-filter">
                                    <option value="all">All Transactions</option>
                                    <option value="deposit">Deposits</option>
                                    <option value="withdraw">Withdrawals</option>
                                    <option value="transfer">Transfers</option>
                                    <option value="loan">Loans</option>
                                </select>
                                <button id="refresh-history" class="btn btn-secondary">
                                    <i class="fas fa-sync"></i>
                                    Refresh
                                </button>
                            </div>

                            <div class="transaction-list" id="transaction-list">
                                <div class="loading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>Loading transactions...</span>
                                </div>
                            </div>

                            <div class="pagination">
                                <button id="prev-page" class="btn btn-secondary" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                    Previous
                                </button>
                                <span id="page-info">Page 1</span>
                                <button id="next-page" class="btn btn-secondary">
                                    Next
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Processing...</span>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Confirm Transaction</h3>
                <button id="modal-close" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p id="modal-message">Are you sure you want to proceed?</p>
                <div id="modal-details"></div>
            </div>
            <div class="modal-footer">
                <button id="modal-cancel" class="btn btn-secondary">Cancel</button>
                <button id="modal-confirm" class="btn btn-primary">Confirm</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
